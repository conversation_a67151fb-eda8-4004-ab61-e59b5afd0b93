import { appDomain, domain } from "@/constants";
import { CompanyType, IndustryType, DesignationType } from "@/types";
import axios from "axios";

export const getAllCompanies = async (search: string): Promise<CompanyType[]> => {
    try {
        const response = await axios.get(`${appDomain}/api/mapping/v1/company-master/all-company?page=1&search=${search}&industry=&employeeSize=&logo=undefined`);
        return response.data.data.companies;
    } catch (error) {
        throw error;
    }
}

export const getAllJobTitles = async (search: string): Promise<DesignationType[]> => {
    try {
        const response = await axios.get(`${appDomain}/api/mapping/v1/designation-master/all-designation?page=1&search=${search}`);
        return response.data.data;
    } catch (error) {
        throw error;
    }
}

export const getAllIndustries = async (): Promise<IndustryType[]> => {
    try {
        const response = await axios.get(`${domain}/api/get-industries`);
        return response.data.data;
    } catch (error) {
        throw error;
    }
}